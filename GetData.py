import pandas as pd
import numpy as np
import requests
import os # Import the os module to check for file existence

# Kraken OHLCV endpoint
url = "https://api.kraken.com/0/public/OHLC"

class GetData:
    def __init__(self):
        self.response = None 

    def get_historical_data(self, symbol='SOLUSD', interval_minutes=240, since=0):
        """
        Fetches historical data from the Kraken OHLC endpoint since a given timestamp.
        
        Parameters:
        symbol (str): The Kraken pair symbol (e.g., 'SOLUSD', 'XBTUSD').
        interval_minutes (int): The OHLC interval in minutes (e.g., 60 for 1 hour, 240 for 4 hours).
        since (int): The timestamp (in seconds) from which to retrieve data.
        
        Returns:
        pd.DataFrame: A DataFrame containing the historical OHLC data, or None if an error occurs.
        """
        parameters = {
            "pair": symbol,
            "interval": interval_minutes,
            "since": since
        }
        
        # Add the 'since' parameter only if it's provided
        if since is not None:
            parameters["since"] = since

        try:
            print(f"Fetching data for {symbol} with interval {interval_minutes} minutes...")
            self.response = requests.get(url, params=parameters)
            self.response.raise_for_status()
            data = self.response.json()
            
            if data['error']:
                print(f"Kraken API error: {data['error']}")
                return None
            
            # Check if any data was returned
            if not data['result'] or not [k for k in data['result'].keys() if k != 'last']:
                print("No new data available from the API.")
                return pd.DataFrame() # Return an empty DataFrame

            key = [k for k in data['result'].keys() if k != 'last'][0]
            ohlc_data = data['result'][key]
            
            df = pd.DataFrame(ohlc_data, columns=["time", "open", "high", "low", "close", "vwap", "volume", "count"])

            df['time'] = pd.to_datetime(df['time'].astype(float), unit='s')
            df[['open', 'high', 'low', 'close', 'vwap', 'volume']] = df[['open', 'high', 'low', 'close', 'vwap', 'volume']].astype(float)
            df['count'] = df['count'].astype(int)

            print("Data fetched successfully!")
            
            return df

        except requests.exceptions.RequestException as e:
            print(f"Error during API request: {e}")
            return None
        except KeyError as e:
            print(f"Error parsing JSON response. Missing key: {e}")
            print("Response data:")
            print(data)
            return None
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            return None
            
    def update_historical_data(self, symbol='SOLUSD', interval_minutes=240, file_path='historical_data.csv'):
        """
        Fetches new historical data from Kraken and appends it to an existing CSV file.
        
        Parameters:
        symbol (str): The Kraken pair symbol.
        interval_minutes (int): The OHLC interval in minutes.
        file_path (str): The path to the CSV file.
        """
        
        if os.path.exists(file_path):
            print(f"File '{file_path}' found. Reading the latest record...")
            try:
                # Read the last row of the CSV file
                existing_df = pd.read_csv(file_path, parse_dates=['time'])
                last_record_time = existing_df['time'].iloc[-1]
                
                # Convert the datetime object to a Unix timestamp (seconds) and add 1
                since_timestamp = int(last_record_time.timestamp()) + 1
                
                print(f"Latest record in file is at time: {last_record_time}")
                print(f"Fetching data since timestamp: {since_timestamp}")

                new_data_df = self.get_historical_data(symbol=symbol, interval_minutes=interval_minutes, since=since_timestamp)
                
                if new_data_df is not None and not new_data_df.empty:
                    # Append new data to the CSV file
                    print(f"Appending {len(new_data_df)} new records to '{file_path}'.")
                    new_data_df.to_csv(file_path, mode='a', header=False, index=False)
                    print("CSV file updated successfully.")
                else:
                    print("No new records to append. CSV file is already up to date.")
                    
            except Exception as e:
                print(f"Error updating the file: {e}")
        else:
            print(f"File '{file_path}' not found. Fetching all available data to create the file.")
            initial_df = self.get_historical_data(symbol=symbol, interval_minutes=interval_minutes)
            
            if initial_df is not None:
                # Save the initial data to a new CSV file
                print(f"Saving {len(initial_df)} records to a new file: '{file_path}'.")
                initial_df.to_csv(file_path, index=False)
                print("Initial historical data saved successfully.")

# --- Example Usage ---
# Create an instance of the class
dataset_updater = GetData()
dataset_updater.get_historical_data

# Set the parameters
crypto_symbol = 'SOLUSD'
timeframe = 240 # 4 hours
data_file = 'sol_usd_historical_data.csv'

# Call the function to either create the file or append to it
dataset_updater.update_historical_data(symbol=crypto_symbol, interval_minutes=timeframe, file_path=data_file)